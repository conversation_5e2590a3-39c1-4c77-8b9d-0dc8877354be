import { ISchedule, ScheduleKey } from '../types';

export const localSearches: ISchedule = {
    name: 'Local Searches',
    errors: [],
    isBeta: true,
    isComingSoon: false,
    isDisabled: false,
    isNew: true,
    allPurchased: false,
    description: 'Upload your digital local searchSchedules to include analysis of these documents in your report pack',
    hasPreview: false,
    isEmpty: false,
    isUpdated: false,
    items: [],
    key: ScheduleKey.localSearches,
    messages: [],
    provider: 'Local Searches',
    totalCost: '\u00a30.00',
    type: 'Local Searches',
    unitCost: '\u00a30.00',
    realProgress: 0,
    possibleDuplicates: {},
};

export const otherSearches: ISchedule = {
    name: 'Other Searches',
    errors: [],
    isBeta: true,
    isComingSoon: false,
    isDisabled: false,
    isNew: true,
    allPurchased: false,
    description: 'Upload other types of property searchSchedules to include analysis of these documents in your report pack',
    hasPreview: false,
    isEmpty: false,
    isUpdated: false,
    items: [],
    key: ScheduleKey.otherSearches,
    messages: [],
    provider: 'Other Searches',
    totalCost: '\u00a30.00',
    type: 'Other Searches',
    unitCost: '\u00a30.00',
    realProgress: 0,
    possibleDuplicates: {},
};

export const searchSchedules: ISchedule[] = [
    localSearches,
    otherSearches,
];
