import { Injectable } from '@angular/core';
import { HttpClient, HttpResponse } from '@angular/common/http';
import { IPurchasedFileBase, ISchedulePurchaseParams, IScheduleType, IScheduleTypeInfo } from '../types';
import { map, Observable, of, switchMap } from 'rxjs';
import { FiledCopyApi } from './filed-copy.api';

@Injectable()
export class SchedulesApi {

    constructor(
        private readonly http: HttpClient,
        private readonly filedCopiesApi: FiledCopyApi,
    ) {
    }

    public getSchedulesTypeList(folderId: string): Observable<IScheduleType[]> {
        return this.http.get<IScheduleType[]>(`api/titles/schedules/${folderId}`);
    }

    public getScheduleTypeInfo(url: string): Observable<IScheduleTypeInfo> {
        return this.http.get<IScheduleTypeInfo>(url);
    }

    public startSchedulePurchase(folderId: string, key: string, params: ISchedulePurchaseParams[]): Observable<HttpResponse<void>> {
        return this.http.post<void>(`api/schedules/purchase/${key}/${folderId}`, params, { observe: 'response' });
    }

    public getSchedulePurchaseStatus(url: string): Observable<HttpResponse<IPurchasedFileBase[]>> {
        return this.http.get<IPurchasedFileBase[]>(url, { observe: 'response' });
    }
}
