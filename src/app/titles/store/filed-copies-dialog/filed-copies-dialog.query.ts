import { Injectable } from '@angular/core';
import { Query } from '@datorama/akita';
import { FiledCopiesDialogState, FiledCopiesDialogStore } from './filed-copies-dialog.store';
import { Observable, switchMap } from 'rxjs';
import { map } from 'rxjs/operators';
import { SchedulesQuery } from '../schedules';
import { IScheduleItem } from '../../types';
import { LinkedDocumentItem } from '../../types/linked-document-item.type';
import { AvailabilityOption } from '../../enums/availability-option.enum';
import { FilterField } from '../../modules/filed-copies/enums/filter-field.enum';
import { FiledCopiesDuplicatesService } from '../../modules/filed-copies/services/filed-copies-duplicates.service';
import { GroupItem } from '../../modules/filed-copies/types/group-item.type';

@Injectable()
export class FiledCopiesDialogQuery extends Query<FiledCopiesDialogState> {

    public readonly selectAll = this.schedulesQuery.selectLinkedDocumentsItems();

    constructor(
        protected readonly store: FiledCopiesDialogStore,
        private readonly schedulesQuery: SchedulesQuery,
        private readonly duplicatesService: FiledCopiesDuplicatesService,
    ) {
        super(store);
    }

    public selectAllDocumentsWithFiles(): Observable<IScheduleItem[]> {
        return this.selectAll.pipe(
            map((documents) => documents.filter((document) => !!document.file)),
        );
    }

    public getAll(): IScheduleItem[] {
        return this.schedulesQuery.getLinkedDocumentsItems();
    }

    public selectCount(predicate: (value: IScheduleItem, index: number, array: IScheduleItem[]) => boolean): Observable<number> {
        return this.select().pipe(
            switchMap(() => this.selectAll),
            map((items) => items.filter(predicate)),
            map((items) => items.length),
        );
    }

    public selectCountOfAvailableForPurchaseItems(predicate?: (value: IScheduleItem) => boolean): Observable<number> {
        return this.selectCount((item) => item.offerPurchase && item.isAvailable && this.filterPredicate(item) && (!predicate || predicate(item)));
    }

    public getAvailableForPurchaseItems(predicate: (value: IScheduleItem) => boolean): IScheduleItem[] {
        return this.getAll()
            .filter((item) => item.offerPurchase && item.isAvailable && this.filterPredicate(item) && predicate(item));
    }

    public selectIsAvailableItemExist(predicate: (value: IScheduleItem) => boolean): Observable<boolean> {
        return this.selectCount((item) => item.isAvailable && this.filterPredicate(item) && predicate(item))
            .pipe(
                map((count) => !!count),
            );
    }

    public getCount(predicate: (value: IScheduleItem, index: number, array: IScheduleItem[]) => boolean): number {
        return this.getAll().filter(predicate).length;
    }

    public isSelected(item: IScheduleItem): boolean {
        return this.getValue().selectedItemIds.includes(item.id);
    }

    public isAllAvailableToSelectItemsSelected$(): Observable<boolean> {
        return this.select()
            .pipe(
                switchMap(() => this.selectCount((item) => item.offerPurchase && item.isAvailable && this.filterPredicate(item) && !this.isSelected(item))),
                map((unselectedAmount) => !unselectedAmount),
            );
    }

    public isAllAvailableToSelectItemsSelected(): boolean {
        const unselectedAmount = this.getCount((item) => item.offerPurchase && item.isAvailable && this.filterPredicate(item) && !this.isSelected(item));

        return !unselectedAmount;
    }

    public isAtLeastOneItemSelected$(): Observable<boolean> {
        return this.selectCount((item) => this.isSelected(item) && item.isAvailable && item.offerPurchase)
            .pipe(
                map((selectedAmount) => !!selectedAmount),
            );
    }

    public getSelectedAvailableToSelectItems(): LinkedDocumentItem[] {
        return this.getAll().filter((item) => this.isSelected(item) && item.isAvailable && item.offerPurchase)
            .map((item) => ({ ...item, isSelected: true }));
    }

    public getFilteredItems$(): Observable<LinkedDocumentItem[]> {
        return this.select()
            .pipe(
                switchMap(() => this.selectAll),
                map((items) => this.filterFiledCopies(items)),
                map((items) => items.map((item) => ({ ...item, isSelected: this.isSelected(item) }))),
            );
    }

    public selectFilteredGroupedItems(): Observable<GroupItem<LinkedDocumentItem>[]> {
        return this.getFilteredItems$()
            .pipe(
                map((items) =>
                    this.getValue().isDuplicatesGrouped
                        ? this.duplicatesService.assignGroupAttributes(items)
                        : items.map((item) => this.duplicatesService.assignDefaultAttributes(item)),
                ),
            );
    }

    public getAvailableToSelectItems(): LinkedDocumentItem[] {
        const availableItems = this.getAll().filter((item) => item.isAvailable && item.offerPurchase);

        return this.filterFiledCopies(availableItems);
    }

    public filterPredicate(item: IScheduleItem): boolean {
        const filters = [
            this.availabilityFilter,
            this.filterByParentTitlePredicate,
            this.filterByFiledUnderPredicate,
            this.filterByTitlePredicate,
            this.filterByTypePredicate,
            this.filterByLocationPredicate,
            this.filterByDateFromPredicate,
            this.filterByDateToPredicate,
            this.filterByDatePredicate,
        ];

        return filters.every((filter) => filter.bind(this, item)());
    }

    private filterFiledCopies(filedCopies: IScheduleItem[]): LinkedDocumentItem[] {
        return filedCopies.filter(this.filterPredicate.bind(this));
    }

    private filterByTypePredicate(item: IScheduleItem): boolean {
        const filter = this.getValue().filters[FilterField.type];
        const filterValue = filter?.value as string[];

        if (!filterValue?.length) {
            return true;
        }

        return filterValue.includes(item.documentType);
    }

    private filterByParentTitlePredicate(item: IScheduleItem): boolean {
        const filter = this.getValue().filters[FilterField.parentTitle];
        const filterValue = (filter?.value as string)?.toLowerCase();

        if (!filterValue) {
            return true;
        }

        return item.reference.toLowerCase().includes(filterValue);
    }

    private filterByFiledUnderPredicate(item: IScheduleItem): boolean {
        const filter = this.getValue().filters[FilterField.filedUnder];
        const filterValue = filter?.value as string[];

        if (!filterValue?.length) {
            return true;
        }

        return filterValue.includes(item.filedUnder);
    }

    private filterByTitlePredicate(item: IScheduleItem): boolean {
        const filter = this.getValue().filters[FilterField.title];
        const filterValue = (filter?.value as string)?.toLowerCase();

        if (!filterValue) {
            return true;
        }

        return item.reference.toLowerCase().includes(filterValue)
            || item.filedUnder.toLowerCase().includes(filterValue);
    }

    private filterByLocationPredicate(item: IScheduleItem): boolean {
        const filter = this.getValue().filters[FilterField.location];
        const filterValue = filter?.value as string[];

        if (!filterValue?.length) {
            return true;
        }

        return filterValue.some((location) => item.entryNumbers.some((number) => number.includes(location)));
    }

    private filterByDatePredicate(item: IScheduleItem): boolean {
        const filter = this.getValue().filters[FilterField.date];
        const filterValue = filter?.value as string;

        if (!filterValue) {
            return true;
        }

        const filterDate = new Date(filterValue);
        const itemDate = new Date(item.documentDate);
        const clearedFilterDate = this.getDateOnly(filterDate);
        const clearedItemDate = this.getDateOnly(itemDate);

        return clearedFilterDate.getTime() === clearedItemDate.getTime();
    }

    private filterByDateFromPredicate(item: IScheduleItem): boolean {
        const filter = this.getValue().filters[FilterField.dateFrom];
        const filterValue = filter?.value as string;

        if (!filterValue) {
            return true;
        }

        const filterDate = new Date(filterValue);
        const itemDate = new Date(item.documentDate);
        const clearedFilterDate = this.getDateOnly(filterDate);
        const clearedItemDate = this.getDateOnly(itemDate);

        return clearedFilterDate.getTime() <= clearedItemDate.getTime();
    }

    private filterByDateToPredicate(item: IScheduleItem): boolean {
        const filter = this.getValue().filters[FilterField.dateTo];
        const filterValue = filter?.value as string;

        if (!filterValue) {
            return true;
        }

        const filterDate = new Date(filterValue);
        const itemDate = new Date(item.documentDate);
        const clearedFilterDate = this.getDateOnly(filterDate);
        const clearedItemDate = this.getDateOnly(itemDate);

        return clearedFilterDate.getTime() >= clearedItemDate.getTime();
    }

    private getDateOnly(date: Date): Date {
        return new Date(date.getFullYear(), date.getMonth(), date.getDate());
    }

    private availabilityFilter(item: IScheduleItem): boolean {
        const filterUnavailable = this.getValue().filterAvailability;

        switch (filterUnavailable) {
            case AvailabilityOption.available:
                return item.isAvailable;
            case AvailabilityOption.unavailable:
                return !item.isAvailable;
            default:
                return true;
        }
    }
}
