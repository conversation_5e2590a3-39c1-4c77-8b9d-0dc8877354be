import { Injectable } from '@angular/core';
import { QueryEntity } from '@datorama/akita';
import { SchedulesState, SchedulesStore } from './schedules.store';
import { ISchedule, IScheduleItem, ScheduleKey } from '../../types';
import { map, Observable } from 'rxjs';

@Injectable()
export class SchedulesQuery extends QueryEntity<SchedulesState> {

    constructor(
        protected readonly store: SchedulesStore,
    ) {
        super(store);
    }

    public selectIsAnyActionInProgress(): Observable<boolean> {
        return this.select('actionsInProgressAmount')
            .pipe(
                map((amount) => !!amount),
            );
    }

    public selectLinkedDocumentsItems(): Observable<IScheduleItem[]> {
        return this.selectEntity(ScheduleKey.linkedDocuments, (entity) => entity.items ?? []);
    }

    public getLinkedDocumentsItems(): IScheduleItem[] {
        return this.getEntity(ScheduleKey.linkedDocuments)?.items ?? [];
    }

    public getLinkedDocuments(): ISchedule {
        return this.getEntity(ScheduleKey.linkedDocuments);
    }
}
