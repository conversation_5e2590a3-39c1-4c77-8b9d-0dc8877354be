@use 'assets/sass/variables' as *;

.entry-texts-container {
    padding: 0.86em;
    background-color: rgba(lighten($gray, 15%), 0.4);
    border-radius: $primary-border-radius;
}

.entry-texts-grid {
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: 0.86em;
    align-items: start;
}

.entry-key {
    min-width: 35px;
}

.entry-value {
    word-wrap: break-word;
    overflow-wrap: break-word;
    white-space: pre-wrap;
}

.entry-separator {
    grid-column: 1 / -1;
    height: 1px;
    background-color: lighten($gray, 12%);
}
