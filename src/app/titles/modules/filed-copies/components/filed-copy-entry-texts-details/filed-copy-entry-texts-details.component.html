<div class="entry-texts-container">
    <div class="entry-texts-grid">
        <ng-container *ngFor="let key of getKeys(); let i = index">
            <div class="entry-key">{{ key }}</div>
            <div class="entry-value">{{ entryTexts[key] }}</div>
            <div
                *ngIf="i < getKeys().length - 1"
                class="entry-separator"
            ></div>
        </ng-container>
    </div>
</div>
