import { NgModule } from '@angular/core';
import { FiledCopiesFilterComponent } from './components/filed-copies-filter/filed-copies-filter.component';
import { SharedModule } from '@shared/shared.module';
import { FilterInputComponent } from './components/filter-input/filter-input.component';
import { FormsModule } from '@angular/forms';
import { FiledCopiesDuplicatesService } from './services/filed-copies-duplicates.service';
import { FiledCopyEntryTextsDetailsComponent } from './components/filed-copy-entry-texts-details/filed-copy-entry-texts-details.component';


@NgModule({
    declarations: [
        FiledCopiesFilterComponent,
        FilterInputComponent,
        FiledCopyEntryTextsDetailsComponent,
    ],
    imports: [
        SharedModule,
        FormsModule,
    ],
    exports: [
        FiledCopiesFilterComponent,
        FilterInputComponent,
        FiledCopyEntryTextsDetailsComponent,
    ],
    providers: [
        FiledCopiesDuplicatesService,
    ],
})
export class FiledCopiesModule {
}
