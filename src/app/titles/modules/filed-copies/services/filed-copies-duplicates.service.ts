import { Injectable } from '@angular/core';
import { LinkedDocumentItem } from '../../../types/linked-document-item.type';
import { duplicateColors } from '../constants/duplicate-colors.constant';
import { GroupItem } from '../types/group-item.type';
import { SchedulesQuery } from '../../../store';

@Injectable()
export class FiledCopiesDuplicatesService {

    constructor(
        private readonly schedulesQuery: SchedulesQuery,
    ) {
    }

    public getPossibleDuplicates(): { [key: string]: string[] } {
        return this.schedulesQuery.getLinkedDocuments().possibleDuplicates ?? {};
    }

    public assignGroupAttributes(items: LinkedDocumentItem[]): GroupItem<LinkedDocumentItem>[] {
        const duplicates = this.getPossibleDuplicates();
        const groups = this.groupDuplicates(items, duplicates);
        const result: GroupItem<LinkedDocumentItem>[] = this.assignColorsToDuplicates(groups);

        groups.forEach((groupItems) => {
            if (groupItems.length === 1) {
                result.push(this.assignDefaultAttributes(groupItems[0]));
            }
        });

        return result;
    }

    public assignDefaultAttributes(item: LinkedDocumentItem): GroupItem<LinkedDocumentItem> {
        return {
            ...item,
            bgColor: null,
            borderColor: null,
            isFirst: false,
            isLast: false,
        };
    }

    private groupDuplicates(items: LinkedDocumentItem[], duplicates: { [key: string]: string[] }): Map<string, LinkedDocumentItem[]> {
        const groups = new Map<string, LinkedDocumentItem[]>();
        const processedIds = new Set<string>();

        items.forEach((item) => {
            if (processedIds.has(item.id)) {
                return;
            }

            const duplicateIds = duplicates[item.id];
            if (duplicateIds && duplicateIds.length > 0) {
                const groupKey = item.id;

                if (!groups.has(groupKey)) {
                    groups.set(groupKey, []);
                }

                groups.get(groupKey).push(item);
                processedIds.add(item.id);

                duplicateIds.forEach((duplicateId) => {
                    if (processedIds.has(duplicateId)) {
                        return;
                    }

                    const duplicateItem = items.find((element) => element.id === duplicateId);
                    if (duplicateItem) {
                        groups.get(groupKey).push(duplicateItem);
                        processedIds.add(duplicateId);
                    }
                });
            } else {
                groups.set(item.id, [item]);
                processedIds.add(item.id);
            }
        });

        return groups;
    }

    private assignColorsToDuplicates(groups: Map<string, LinkedDocumentItem[]>): GroupItem<LinkedDocumentItem>[] {
        const result: GroupItem<LinkedDocumentItem>[] = [];
        let groupIndex = 0;

        groups.forEach((groupItems) => {
            if (groupItems.length > 1) {
                const groupColors = duplicateColors[groupIndex % duplicateColors.length];
                groupIndex++;

                groupItems.forEach((item, index) => {
                    result.push({
                        ...item,
                        bgColor: groupColors.background,
                        borderColor: groupColors.border,
                        isFirst: index === 0,
                        isLast: index === groupItems.length - 1,
                    });
                });
            }
        });

        return result;
    }
}
