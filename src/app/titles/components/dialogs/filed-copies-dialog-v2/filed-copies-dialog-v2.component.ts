import { Component, ElementRef, Inject, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { mergeWith, Observable, Subject } from 'rxjs';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import {
    FiledCopiesDialogQuery,
    FiledCopiesDialogService,
    FiledCopiesDialogStore,
    FiledCopiesUploadQuery,
    FiledCopiesUploadService,
    SchedulesQuery,
} from '../../../store';
import { filter, finalize, map, takeUntil, tap } from 'rxjs/operators';
import { ISchedule, IScheduleItem, ScheduleKey } from '../../../types';
import { AnimatedButtonState } from '@shared/components/animated-button-five-states/enums/animated-button-state.enum';
import { PURCHASE_ALL_CONFIRM_DATA } from '@constants';
import { ConfirmDialogComponent } from '@shared/components/dialogs/confirm-dialog/confirm-dialog.component';
import { SchedulesService } from '../../../store/schedules/schedules.service';
import { AvailabilityOption } from '../../../enums/availability-option.enum';
import { FilterOptionsList, FilterValuesList } from '../../../modules/filed-copies/types/filters-list.type';
import { FilterField } from '../../../modules/filed-copies/enums/filter-field.enum';
import { FiledCopyUploadButtonState } from '../../../enums/filed-copy-upload-button-status.enum';
import { ProfileService } from '@services';
import { ErrorDetails } from '@shared/components/alerts/expandable-errors-alert/expandable-errors-alert.component';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { TourControllerService } from '../../../modules/filed-copies-tour/services/tour-controller.service';
import { InfoSnackbarComponent } from '@shared/components/info-snackbar/info-snackbar.component';
import { MatSnackBar } from '@angular/material/snack-bar';
import { FileUploadersMap } from '../../../modules/filed-copies/types/file-uploader-item.type';
import { ProjectDetailsQuery } from '../../../../project-details/stores/project-details/project-details.query';
import { GroupItem } from '../../../modules/filed-copies/types/group-item.type';
import { LinkedDocumentItem } from '../../../types/linked-document-item.type';

@Component({
    selector: 'avl-filed-copies-dialog-v2',
    templateUrl: './filed-copies-dialog-v2.component.html',
    styleUrls: ['./filed-copies-dialog-v2.component.scss'],
    animations: [
        trigger('detailsExpand', [
            state('collapsed', style({ height: '0', minHeight: '0', padding: 0 })),
            state('expanded', style({ height: '*', display: '*' })),
            transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
        ]),
        trigger('fadeIn', [
            transition(':enter', [style({ opacity: 0 }), animate('200ms ease-in', style({ opacity: 1 }))]),
        ]),
    ],
})
export class FiledCopiesDialogV2Component implements OnInit, OnDestroy {
    public readonly availabilityOptions = [
        { id: AvailabilityOption.all, label: 'All' },
        { id: AvailabilityOption.available, label: 'Available' },
        { id: AvailabilityOption.unavailable, label: 'Unavailable' },
    ];

    public readonly purchaseAllErrorsId = 'purchaseAll';
    public readonly uploadColumnId = 'upload';
    public readonly filtersOptions: FilterOptionsList<string> = {};
    public readonly uploadingFiledCopyIds: string[] = [];
    public draggedRowFiledCopyId: string | null = null;
    public displayedColumns: string[] = ['toggle', 'reference', 'type', 'filedUnder', 'location', 'documentDate', 'purchase'];
    public filteredDataSource$: Observable<GroupItem<LinkedDocumentItem>[]>;
    public folderId: string;
    public itemPrice = '';
    public unitCost = 0;
    public currencySymbol = '';
    public totalCost$: Observable<string>;
    public selectedAvailabilityOptionId = AvailabilityOption.all;
    public hasScroll = false;
    public isAvailableItemExist$: Observable<boolean>;
    public isUploadEnabled$: Observable<boolean>;
    public fileUploaders: FileUploadersMap = {};
    public isDuplicatesGrouped$: Observable<boolean>;
    public extendedEntryTextsRowId: string | null = null;

    @ViewChild('dialogContentRef')
    private readonly dialogContentRef: ElementRef<HTMLElement>;

    private readonly countdownPendingItems = new Set<string>();
    private readonly purchasingItemIds = new Set<string>();
    private readonly destroy$ = new Subject<void>();
    private dragEnterCount = 0;
    private itemPurchaseErrors: { [key: string]: ErrorDetails[] } = {};
    private itemUploadErrors: { [key: string]: ErrorDetails[] } = {};
    private isDocumentPurchased = false;
    private isDocumentUploaded = false;


    constructor(
        private readonly filedCopiesDialogService: FiledCopiesDialogService,
        private readonly dialogRef: MatDialogRef<FiledCopiesDialogV2Component>,
        private readonly filedCopiesDialogStore: FiledCopiesDialogStore,
        private readonly filedCopiesDialogQuery: FiledCopiesDialogQuery,
        private readonly projectDetailsQuery: ProjectDetailsQuery,
        private readonly schedulesService: SchedulesService,
        private readonly schedulesQuery: SchedulesQuery,
        private readonly dialog: MatDialog,
        @Inject(MAT_DIALOG_DATA)
        private readonly data: {
            updateLinkedDocuments: () => Observable<void>;
        },
        private readonly filedCopiesUploadQuery: FiledCopiesUploadQuery,
        private readonly filedCopiesUploadService: FiledCopiesUploadService,
        private readonly profileService: ProfileService,
        private readonly filedCopiesTourService: TourControllerService,
        private readonly snackBar: MatSnackBar,
    ) {
        this.itemPrice = this.schedule.unitCost;
    }

    public get isCloseButtonDisabled(): boolean {
        const isPurchasingInProgress = !!this.purchasingItemIds.size;
        const isPendingItems = !!this.countdownPendingItems.size;
        const isUploadingItem = !!this.uploadingFiledCopyIds.length;

        return isPurchasingInProgress || isPendingItems || isUploadingItem;
    }

    public get schedule(): ISchedule {
        return this.schedulesQuery.getEntity(ScheduleKey.linkedDocuments);
    }

    public ngOnInit(): void {
        this.isDuplicatesGrouped$ = this.filedCopiesDialogQuery.select('isDuplicatesGrouped');
        this.isUploadEnabled$ = this.profileService.isFiledCopiesUploadEnabled$.asObservable();
        this.isAvailableItemExist$ = this.filedCopiesDialogQuery.selectIsAvailableItemExist(this.filterByPurchasingPredicate.bind(this));
        this.folderId = this.projectDetailsQuery.projectId;
        this.profileService.isFiledCopiesUploadEnabled$.asObservable()
            .pipe(takeUntil(this.destroy$))
            .subscribe((isEnabled) => {
                this.displayedColumns = this.displayedColumns.filter((column) => column !== this.uploadColumnId);

                if (isEnabled) {
                    const indexBeforeLastElement = this.displayedColumns.length - 1;
                    this.displayedColumns.splice(indexBeforeLastElement, 0, this.uploadColumnId);
                }
            });
        this.filteredDataSource$ = this.filedCopiesDialogQuery.selectFilteredGroupedItems()
            .pipe(
                tap((items) => {
                    console.log('items', items);
                }),
            );
        this.filedCopiesUploadService.fileUploaded$
            .pipe(takeUntil(this.destroy$))
            .subscribe(() => {
                this.isDocumentUploaded = true;
                this.refetchDocuments();
            });
        this.filedCopiesUploadQuery.selectItemErrors()
            .pipe(takeUntil(this.destroy$))
            .subscribe((errorDetails) => {
                let lastNewErrorItemId = null;
                const errorItemIds = errorDetails.map((error) => error.itemId);

                errorDetails.forEach((error) => {
                    const isNewError = !this.itemUploadErrors[error.itemId];

                    this.itemUploadErrors[error.itemId] = error.messages.map((details) => ({
                        id: error.reference,
                        title: error.formatedReference,
                        description: details?.message ?? '',
                    }));

                    if (isNewError) {
                        lastNewErrorItemId = error.itemId;
                    }
                });

                if (lastNewErrorItemId) {
                    setTimeout(() => this.scrollToErrorIfNeeded(this.convertToValidId(lastNewErrorItemId)), 300);
                }

                this.filedCopiesUploadService.clearLinkingFailedItems(errorItemIds);
            });
        this.totalCost$ = this.filedCopiesDialogQuery.selectCountOfAvailableForPurchaseItems(this.filterByPurchasingPredicate.bind(this))
            .pipe(
                map((count) => `${this.currencySymbol}${count * this.unitCost}`),
            );
        this.unitCost = this.extractUnitCost();
        this.currencySymbol = this.extractCurrencySymbol();
        this.filedCopiesUploadQuery.selectAll()
            .pipe(
                takeUntil(this.destroy$),
                mergeWith(this.filedCopiesUploadQuery.select('selectedToUploadFiledCopy')),
            )
            .subscribe(() => {
                const uploadingFiledCopies = this.filedCopiesUploadQuery.getAll();
                this.syncUploadingFiledCopies(uploadingFiledCopies);
            });
        this.extractFiltersOptions();
        this.filedCopiesDialogQuery.selectAllDocumentsWithFiles()
            .pipe(takeUntil(this.destroy$))
            .subscribe((documents) => {
                const ids = documents.map((document) => document.id);
                this.filedCopiesUploadService.clearUploadedRecords(ids);
            });

        this.startUiTour();
        this.handleEscapeKey();
        this.generateFileUploaders();
    }

    public ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();
        this.filedCopiesDialogStore.resetFilters();
        this.filedCopiesUploadService.unregisterAllFileUploader();
    }

    public onFilterUpdated(filters: FilterValuesList): void {
        this.filedCopiesDialogStore.update({ filters: filters });
    }

    public onAvailabilityFilterChanged(id: AvailabilityOption): void {
        this.selectedAvailabilityOptionId = id;
        this.filedCopiesDialogStore.update({ filterAvailability: id });
    }

    public onRemoveUploadedDocument(item: IScheduleItem): void {
        const fileId = item.file?.id;

        if (fileId) {
            this.filedCopiesDialogService.deleteUploadedFile(fileId)
                .subscribe(() => this.refetchDocuments());
        }
    }

    public onUploadDocument(item: IScheduleItem, fileSelectInput: HTMLElement): void {
        const cancelCallback = (): void => {
            this.filedCopiesUploadService.cancelFiledCopySelection();
        };

        this.clearErrors(item);
        this.filedCopiesUploadService.selectFiledCopyToUpload(item);
        fileSelectInput.addEventListener('cancel', cancelCallback, { once: true });
        fileSelectInput.addEventListener('change', () => {
            fileSelectInput.removeEventListener('cancel', cancelCallback);
        }, { once: true });
        fileSelectInput.click();
    }

    public openDocumentInNewTab(item: IScheduleItem): void {
        const folderId = this.folderId;
        const documentId = item.file.id;
        const url = this.filedCopiesDialogService.getDocumentViewPath(folderId, documentId);

        window.open(url, '_blank');
    }

    public isItemUploading(item: IScheduleItem): boolean {
        return this.uploadingFiledCopyIds.includes(item.id);
    }

    public isPurchaseAllButtonDisabled(): boolean {
        const isAnyPurchasing = this.isCloseButtonDisabled;

        return isAnyPurchasing;
    }

    public purchaseAllButtonState(): AnimatedButtonState {
        const isOfferForPurchaseExist = !!this.filedCopiesDialogQuery.getAvailableForPurchaseItems(this.filterByPurchasingPredicate.bind(this)).length;
        const isErrors = !!this.itemPurchaseErrors[this.purchaseAllErrorsId]?.length;
        const isPurchasingOrPending = !!this.countdownPendingItems.size || !!this.purchasingItemIds.size;

        if (isErrors) {
            return AnimatedButtonState.failure;
        }

        if (isOfferForPurchaseExist || isPurchasingOrPending) {
            return AnimatedButtonState.idle;
        }

        return AnimatedButtonState.success;
    }

    public isPurchaseButtonDisabled(item: IScheduleItem): boolean {
        const isItemUploading = this.isItemUploading(item);

        return isItemUploading;
    }

    public purchaseButtonState(item: IScheduleItem): AnimatedButtonState {
        const isPurchased = item.isPurchased;
        const isPurchasingInProgress = this.isItemPurchasing(item);
        const itemError = this.itemPurchaseErrors[item.id];
        const isUploaded = !!item.file;

        if (itemError) {
            return AnimatedButtonState.failure;
        }

        if (isPurchased || isUploaded) {
            return AnimatedButtonState.success;
        }

        if (isPurchasingInProgress) {
            return AnimatedButtonState.loading;
        }

        const isPendingItem = this.countdownPendingItems.has(item.id);
        if (isPendingItem) {
            return AnimatedButtonState.undo;
        }

        return AnimatedButtonState.idle;
    }

    public isUploadButtonDisabled(item: IScheduleItem): boolean {
        const isPurchasingInProgress = this.isItemPurchasing(item);
        const isPendingItem = this.countdownPendingItems.has(item.id);

        return isPurchasingInProgress || isPendingItem;
    }

    public uploadButtonState(item: IScheduleItem): FiledCopyUploadButtonState {
        const isUploaded = !!item.file;
        const isError = item.isFiledCopyError;
        const isPurchased = item.isPurchased;

        if (isError) {
            return FiledCopyUploadButtonState.error;
        }

        if (isPurchased) {
            return FiledCopyUploadButtonState.purchased;
        }

        if (isUploaded) {
            return FiledCopyUploadButtonState.uploaded;
        }

        if (this.isItemUploading(item)) {
            return FiledCopyUploadButtonState.loading;
        }

        return FiledCopyUploadButtonState.initial;
    }

    public itemErrors(item: IScheduleItem): ErrorDetails[] {
        return this.itemPurchaseErrors[item.id] ?? this.itemUploadErrors[item.id] ?? [];
    }

    public purchaseAllErrors(): ErrorDetails[] {
        return this.itemPurchaseErrors[this.purchaseAllErrorsId] ?? [];
    }

    public onClickPurchaseAll(): void {
        const config = { panelClass: 'confirm-dialog', data: PURCHASE_ALL_CONFIRM_DATA };
        const dialog = this.dialog.open(ConfirmDialogComponent, config);

        dialog.afterClosed()
            .pipe(
                filter((isConfirmed) => isConfirmed),
            )
            .subscribe(() => this.purchaseAll());
    }

    public onPurchasingInited(item: IScheduleItem): void {
        this.countdownPendingItems.add(item.id);
    }

    public onPurchasingCanceled(item: IScheduleItem): void {
        this.countdownPendingItems.delete(item.id);
    }

    public purchaseOne(item: IScheduleItem): void {
        this.countdownPendingItems.delete(item.id);
        this.purchasingItemIds.add(item.id);
        this.clearErrors(item);

        this.schedulesService.purchaseLinkedDocument(this.schedule, this.folderId, [item])
            .pipe(
                finalize(() => {
                    this.countdownPendingItems.delete(item.id);
                    this.purchasingItemIds.delete(item.id);
                }),
            )
            .subscribe((purchaseResult) => {
                const firstItem = purchaseResult[0];

                if (firstItem && !firstItem.isError) {
                    this.schedulesService.markDocumentsAsPurchased(this.schedule.key, [item.id]);
                    this.isDocumentPurchased = true;
                } else if (firstItem.isError) {
                    this.itemPurchaseErrors[item.id] = purchaseResult.map((error) => ({
                        id: error.reference,
                        title: error.formattedReference,
                        description: error.message,
                    }));

                    setTimeout(() => this.scrollToErrorIfNeeded(this.convertToValidId(item.id)), 300);
                }

                this.refetchDocuments();
            });
    }

    public close(): void {
        this.dialogRef.close(this.isDocumentPurchased || this.isDocumentUploaded);
    }

    public onScrollDetected(hasScroll: boolean): void {
        setTimeout(() => this.hasScroll = hasScroll);
    }

    public isItemPurchasing(item: IScheduleItem): boolean {
        return this.purchasingItemIds.has(item.id);
    }

    public trackById(index: number, item: IScheduleItem): string {
        return item.id;
    }

    public convertToValidId(id: string): string {
        return id.replace(/[;-]/g, '');
    }

    public markIsDraggingOverRow(item: IScheduleItem): void {
        this.dragEnterCount++;
        this.draggedRowFiledCopyId = item.id;
        this.filedCopiesUploadService.selectFiledCopyToUpload(item);
    }

    public clearIsDraggingOverRow(): void {
        this.dragEnterCount--;

        if (this.dragEnterCount <= 0) {
            this.dragEnterCount = 0;
            this.draggedRowFiledCopyId = null;
        }
    }

    public onFileDrop(row: IScheduleItem): void {
        if (!this.isUploadAvailable(row)) {
            this.filedCopiesUploadService.cancelFiledCopySelection();
        }

        this.clearIsDraggingOverRow();
        this.clearErrors(row);
    }

    public onDragLeave(): void {
        this.clearIsDraggingOverRow();

        if (this.dragEnterCount <= 0) {
            this.filedCopiesUploadService.cancelFiledCopySelection();
        }
    }

    public onGroupToggleChanged(isGrouped: boolean): void {
        this.filedCopiesDialogStore.update({ isDuplicatesGrouped: isGrouped });
    }

    public toggleEntryTexts(row: IScheduleItem): void {
        this.extendedEntryTextsRowId = this.extendedEntryTextsRowId === row.id ? null : row.id;
    }

    public isEntryTextsExist(row: IScheduleItem): boolean {
        return row.entryTexts && !!Object.keys(row.entryTexts).length;
    }

    public isUploadAvailable(item: IScheduleItem): boolean {
        return item && !item.file && !this.isItemUploading(item);
    }

    public restartFiledCopiesTour(): void {
        this.filedCopiesTourService.reset();
        this.filedCopiesTourService.tryToStartTour();
    }

    private purchaseAll(): void {
        const availableToPurchaseItems = this.filedCopiesDialogQuery.getAvailableForPurchaseItems(this.filterByPurchasingPredicate.bind(this));

        if (!availableToPurchaseItems.length) {
            return;
        }

        availableToPurchaseItems.forEach((item) => {
            this.purchasingItemIds.add(item.id);
        });

        this.clearAllErrors();
        this.schedulesService.purchaseLinkedDocument(this.schedule, this.folderId, availableToPurchaseItems)
            .pipe(
                finalize(() => {
                    this.purchasingItemIds.clear();
                }),
            )
            .subscribe((purchaseResult) => {
                const errors = purchaseResult.filter((item) => item.isError);
                this.isDocumentPurchased = true;

                if (errors.length) {
                    this.itemPurchaseErrors[this.purchaseAllErrorsId] = errors.map((error) => ({
                        id: error.reference,
                        title: error.formattedReference,
                        description: error.message,
                    }));
                    this.data.updateLinkedDocuments().subscribe();
                } else {
                    const purchasedItemIds = availableToPurchaseItems.map((item) => item.id);
                    this.schedulesService.markDocumentsAsPurchased(this.schedule.key, purchasedItemIds);
                }

                this.refetchDocuments();
            });
    }

    private extractUnitCost(): number {
        const schedule = this.schedule;
        if (!schedule?.items.length) {
            return 0;
        }

        const digitalRegex = /^(\D*)(.*)/;

        return parseFloat(digitalRegex.exec(schedule.unitCost)[2]);
    }

    private extractCurrencySymbol(): string {
        const schedule = this.schedule;
        if (!schedule?.items.length) {
            return '';
        }

        const digitalRegex = /^(\D*)(.*)/;

        return digitalRegex.exec(schedule.unitCost)[1];
    }

    private filterByPurchasingPredicate({ id }: IScheduleItem): boolean {
        return !this.countdownPendingItems.has(id) && !this.purchasingItemIds.has(id);
    }

    private extractFiltersOptions(): void {
        const rows = this.filedCopiesDialogQuery.getAll();
        const setOfTypes = new Set<string>();
        const setOfReferences = new Set<string>();
        const setOfFiledUnder = new Set<string>();
        let setOfLocations = new Set<string>();

        rows.forEach((item) => {
            setOfTypes.add(item.documentType);
            setOfReferences.add(item.reference);
            setOfFiledUnder.add(item.filedUnder);
            item.entryNumbers.forEach((entry) => setOfLocations.add(entry));
        });

        const uniqueLocations = Array.from(setOfLocations.values());
        setOfLocations = new Set<string>(uniqueLocations.sort(this.locationsSortPredicate));

        this.filtersOptions[FilterField.type] = setOfTypes;
        this.filtersOptions[FilterField.location] = setOfLocations;
        this.filtersOptions[FilterField.parentTitle] = setOfReferences;
        this.filtersOptions[FilterField.filedUnder] = setOfFiledUnder;
    }

    private locationsSortPredicate(a: string, b: string): number {
        const regex = /^([A-Za-z]+)(\d+)$/;

        const matchA = a.match(regex);
        const matchB = b.match(regex);

        if (!matchA || !matchB) {
            return 0;
        }

        const letterA = matchA[1];
        const letterB = matchB[1];

        const numberA = parseInt(matchA[2], 10);
        const numberB = parseInt(matchB[2], 10);

        if (letterA !== letterB) {
            return letterA.localeCompare(letterB);
        }

        return numberA - numberB;
    }

    private syncUploadingFiledCopies(items: IScheduleItem[]): void {
        const newIds = items.map((item) => item.id);

        newIds.forEach((id) => {
            if (!this.uploadingFiledCopyIds.includes(id)) {
                this.uploadingFiledCopyIds.push(id);
            }
        });

        for (let i = this.uploadingFiledCopyIds.length - 1; i >= 0; i--) {
            const rowId = this.uploadingFiledCopyIds[i];

            if (!newIds.includes(rowId)) {
                this.uploadingFiledCopyIds.splice(i, 1);
            }
        }
    }

    private refetchDocuments(): void {
        const folderId = this.projectDetailsQuery.projectId;
        this.schedulesService.fetchLinkedDocumentItems(folderId);
    }

    private clearErrors(item: IScheduleItem): void {
        delete this.itemUploadErrors[item.id];
        delete this.itemPurchaseErrors[item.id];
        delete this.itemPurchaseErrors[this.purchaseAllErrorsId];
    }

    private clearAllErrors(): void {
        this.itemPurchaseErrors = {};
        this.itemPurchaseErrors = {};
    }

    private scrollToErrorIfNeeded(itemId: string): void {
        const errorEl = document.querySelector(`#error-alert-${itemId}`);
        const wrapper = this.dialogContentRef?.nativeElement;

        if (!errorEl || !wrapper) {
            return;
        }

        const errorRect = errorEl.getBoundingClientRect();
        const wrapperRect = wrapper.getBoundingClientRect();

        const isVisible = errorRect.top >= wrapperRect.top
            && errorRect.bottom <= wrapperRect.bottom;

        if (!isVisible) {
            errorEl.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }

    private startUiTour(): void {
        setTimeout(() => {
            const isUploadEnabled = this.profileService.isFiledCopiesUploadEnabled$.getValue();

            if (isUploadEnabled) {
                this.filedCopiesTourService.tryToStartTour();
            }
        }, 400);
    }

    private handleEscapeKey(): void {
        this.dialogRef.keydownEvents()
            .subscribe((event) => {
                if (event.key === 'Escape') {
                    if (this.isCloseButtonDisabled) {
                        this.snackBar.openFromComponent(InfoSnackbarComponent, {
                            data: 'Upload or purchase in progress. Please don’t close this window.',
                            duration: 2500,
                            panelClass: ['info-snackbar', 'min-content'],
                        });
                    } else {
                        this.close();
                    }
                }
            });
    }

    private generateFileUploaders(): void {
        this.filedCopiesDialogQuery.selectAll
            .pipe(takeUntil(this.destroy$))
            .subscribe((items) => {
                items.forEach((item) => {
                    const key = item.id;
                    const fileUploaders = this.filedCopiesUploadService.getFileUploaders();
                    this.fileUploaders[key] = fileUploaders[key] ?? this.filedCopiesUploadService.getOrCreateFileUploader(key);
                });
            });
    }
}

